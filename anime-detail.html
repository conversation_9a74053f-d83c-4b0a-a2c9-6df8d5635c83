<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title><PERSON><PERSON><PERSON> - AnimeVerse</title>
    <link rel="shortcut icon" href="./Assets/fabIcon.png" type="image/x-icon" />
    <link rel="stylesheet" href="style.css" />
    <link rel="stylesheet" href="anime-detail.css" />
    <link
      href="https://cdn.jsdelivr.net/npm/remixicon@4.5.0/fonts/remixicon.css"
      rel="stylesheet"
    />
    <link
      href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Rajdhani:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />
  </head>
  <body>
    <!-- Background Music Toggle -->
    <div class="music-toggle" id="musicToggle">
      <i class="ri-volume-up-line" id="musicIcon"></i>
      <audio id="backgroundMusic" loop>
        <!-- Add your background music file here -->
      </audio>
    </div>

    <!-- Scroll Progress Indicator -->
    <div class="scroll-progress" id="scrollProgress"></div>

    <!-- Navigation -->
    <nav class="navbar" id="navbar">
      <div class="nav-container">
        <div class="logo">
          <img src="./Assets/logo.png" alt="AnimeVerse" />
          <span class="logo-text">AnimeVerse</span>
        </div>
        <div class="nav-menu" id="navMenu">
          <a href="index.html" class="nav-link">
            <i class="ri-home-4-line"></i>
            <span>Home</span>
          </a>
          <a href="index.html#anime-index" class="nav-link">
            <i class="ri-movie-2-line"></i>
            <span>Anime</span>
          </a>
          <a href="#characters" class="nav-link active">
            <i class="ri-user-3-line"></i>
            <span>Characters</span>
          </a>
          <a href="#gallery" class="nav-link">
            <i class="ri-gallery-line"></i>
            <span>Gallery</span>
          </a>
          <a href="#about" class="nav-link">
            <i class="ri-information-line"></i>
            <span>About</span>
          </a>
        </div>
        <div class="nav-toggle" id="navToggle">
          <span></span>
          <span></span>
          <span></span>
        </div>
      </div>
    </nav>

    <main>
      <!-- Hero Section with Anime Background -->
      <section class="anime-hero">
        <div class="anime-hero-bg">
          <img src="./Assets/All1111111.jpeg" alt="Jujutsu Kaisen" />
          <div class="hero-overlay"></div>
        </div>
        <div class="container">
          <div class="anime-hero-content">
            <div class="anime-breadcrumb">
              <a href="index.html">Home</a>
              <i class="ri-arrow-right-s-line"></i>
              <a href="index.html#anime-index">Anime</a>
              <i class="ri-arrow-right-s-line"></i>
              <span>Jujutsu Kaisen</span>
            </div>
            <h1 class="anime-hero-title">Jujutsu Kaisen</h1>
            <div class="anime-hero-meta">
              <span class="anime-genre">Supernatural</span>
              <span class="anime-genre">Action</span>
              <span class="anime-year">2020</span>
              <div class="anime-rating">
                <i class="ri-star-fill"></i>
                <span>9.2</span>
              </div>
            </div>
            <p class="anime-hero-synopsis">
              In a world where cursed spirits feed on unsuspecting humans, fragments of the legendary 
              and feared demon Ryomen Sukuna were lost and scattered about. Should any demon consume 
              Sukuna's body parts, the power they gain could destroy the world as we know it.
            </p>
            <div class="anime-hero-actions">
              <button class="btn btn-primary">
                <i class="ri-play-fill"></i>
                <span>Watch Now</span>
              </button>
              <button class="btn btn-outline">
                <i class="ri-heart-line"></i>
                <span>Add to Favorites</span>
              </button>
              <button class="btn btn-outline">
                <i class="ri-share-line"></i>
                <span>Share</span>
              </button>
            </div>
          </div>
        </div>
      </section>

      <!-- Anime Details Section -->
      <section class="anime-details-section">
        <div class="container">
          <div class="anime-details-grid">
            <!-- Main Content -->
            <div class="anime-main-content">
              <!-- Synopsis -->
              <div class="detail-card">
                <h2 class="detail-title">
                  <i class="ri-file-text-line"></i>
                  Synopsis
                </h2>
                <p class="detail-text">
                  Yuji Itadori is a boy with tremendous physical strength, though he lives a completely ordinary high school life. 
                  One day, to save a classmate who has been attacked by curses, he eats the finger of Ryomen Sukuna, taking the curse into his own soul. 
                  From then on, he shares one body with Ryomen Sukuna. Guided by the most powerful of sorcerers, Satoru Gojo, 
                  Itadori is admitted to Tokyo Jujutsu High School, an organization that fights the curses... 
                  and thus begins the heroic tale of a boy who became a curse to exorcise a curse, a life from which he could never turn back.
                </p>
              </div>

              <!-- Story Arcs -->
              <div class="detail-card">
                <h2 class="detail-title">
                  <i class="ri-timeline-line"></i>
                  Story Arcs
                </h2>
                <div class="story-arcs">
                  <div class="arc-item">
                    <div class="arc-number">01</div>
                    <div class="arc-content">
                      <h3>Fearsome Womb Arc</h3>
                      <p>Episodes 1-5: Yuji's introduction to the world of curses and jujutsu sorcery.</p>
                    </div>
                  </div>
                  <div class="arc-item">
                    <div class="arc-number">02</div>
                    <div class="arc-content">
                      <h3>Cursed Training Arc</h3>
                      <p>Episodes 6-8: Training and development of Yuji's abilities.</p>
                    </div>
                  </div>
                  <div class="arc-item">
                    <div class="arc-number">03</div>
                    <div class="arc-content">
                      <h3>Kyoto Goodwill Event Arc</h3>
                      <p>Episodes 9-16: Competition between Tokyo and Kyoto schools.</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Sidebar -->
            <div class="anime-sidebar">
              <!-- Anime Info -->
              <div class="detail-card">
                <h3 class="detail-title">Anime Information</h3>
                <div class="info-list">
                  <div class="info-item">
                    <span class="info-label">Studio:</span>
                    <span class="info-value">MAPPA</span>
                  </div>
                  <div class="info-item">
                    <span class="info-label">Episodes:</span>
                    <span class="info-value">24</span>
                  </div>
                  <div class="info-item">
                    <span class="info-label">Status:</span>
                    <span class="info-value">Ongoing</span>
                  </div>
                  <div class="info-item">
                    <span class="info-label">Aired:</span>
                    <span class="info-value">Oct 2020 - Mar 2021</span>
                  </div>
                  <div class="info-item">
                    <span class="info-label">Source:</span>
                    <span class="info-value">Manga</span>
                  </div>
                  <div class="info-item">
                    <span class="info-label">Duration:</span>
                    <span class="info-value">24 min per episode</span>
                  </div>
                </div>
              </div>

              <!-- Quick Stats -->
              <div class="detail-card">
                <h3 class="detail-title">Statistics</h3>
                <div class="stats-grid">
                  <div class="stat-item">
                    <span class="stat-number">9.2</span>
                    <span class="stat-label">Rating</span>
                  </div>
                  <div class="stat-item">
                    <span class="stat-number">1.2M</span>
                    <span class="stat-label">Favorites</span>
                  </div>
                  <div class="stat-item">
                    <span class="stat-number">2.5M</span>
                    <span class="stat-label">Views</span>
                  </div>
                  <div class="stat-item">
                    <span class="stat-number">95%</span>
                    <span class="stat-label">Approval</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Characters Section -->
      <section class="characters-section" id="characters">
        <div class="container">
          <div class="section-header">
            <h2 class="section-title">
              <span class="title-accent">Main</span>
              <span class="title-main">Characters</span>
            </h2>
            <p class="section-subtitle">Meet the powerful sorcerers and cursed spirits</p>
          </div>
          
          <div class="characters-grid">
            <!-- Character 1 -->
            <div class="character-card">
              <div class="character-image">
                <img src="./Assets/All1111111.jpeg" alt="Yuji Itadori" />
                <div class="character-overlay">
                  <h3>Yuji Itadori</h3>
                  <p>Main Protagonist</p>
                </div>
              </div>
              <div class="character-info">
                <h4>Yuji Itadori</h4>
                <p>A high school student who becomes the vessel for Sukuna after eating one of his fingers.</p>
              </div>
            </div>

            <!-- Character 2 -->
            <div class="character-card">
              <div class="character-image">
                <img src="./Assets/All1111111.jpeg" alt="Satoru Gojo" />
                <div class="character-overlay">
                  <h3>Satoru Gojo</h3>
                  <p>Special Grade Sorcerer</p>
                </div>
              </div>
              <div class="character-info">
                <h4>Satoru Gojo</h4>
                <p>The strongest jujutsu sorcerer and teacher at Tokyo Jujutsu High School.</p>
              </div>
            </div>

            <!-- Character 3 -->
            <div class="character-card">
              <div class="character-image">
                <img src="./Assets/All1111111.jpeg" alt="Megumi Fushiguro" />
                <div class="character-overlay">
                  <h3>Megumi Fushiguro</h3>
                  <p>First Year Student</p>
                </div>
              </div>
              <div class="character-info">
                <h4>Megumi Fushiguro</h4>
                <p>A first-year student who uses the Ten Shadows Technique.</p>
              </div>
            </div>

            <!-- Character 4 -->
            <div class="character-card">
              <div class="character-image">
                <img src="./Assets/All1111111.jpeg" alt="Nobara Kugisaki" />
                <div class="character-overlay">
                  <h3>Nobara Kugisaki</h3>
                  <p>First Year Student</p>
                </div>
              </div>
              <div class="character-info">
                <h4>Nobara Kugisaki</h4>
                <p>A confident first-year student who uses straw doll technique.</p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>

    <script src="scripts.js"></script>
    <script src="anime-detail.js"></script>
  </body>
</html>
