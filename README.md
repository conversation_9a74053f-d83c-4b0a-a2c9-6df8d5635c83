# AnimeVerse - Digital Artbook Experience

A clean, modern, and visually engaging website that showcases anime series through immersive storytelling. Built with a dark theme and neon/pastel highlights for an anime-cyber aesthetic.

## 🌟 Features

### 🏠 Homepage

- **Full-screen hero section** with video background
- **Featured anime of the week** with detailed information
- **Animated statistics** with counting animations
- **Smooth scroll indicators** and parallax effects
- **Loading screen** with animated logo and progress bar

### 📚 Anime Index

- **Responsive grid layout** showcasing anime series
- **Interactive filtering** by genre (Action, Supernatural, Romance, etc.)
- **Real-time search** functionality
- **Hover animations** with detailed overlays
- **Cover art and teasers** for each anime

### 📖 Anime Detail Pages

- **Comprehensive information** including title, synopsis, and metadata
- **Character biographies** with interactive hover effects
- **Story arcs timeline** with episode breakdowns
- **Studio information** and statistics
- **Dynamic backgrounds** with parallax scrolling

### 🎨 Visual Gallery

- **Tabbed interface** for different content types:
  - Wallpapers (4K quality)
  - Official artwork
  - Episode screenshots
  - Fan art collections
- **Image modal viewer** with download functionality
- **Masonry grid layout** for optimal visual presentation

### 🎵 Interactive Features

- **Background music toggle** with volume controls
- **Scroll progress indicator** at the top of the page
- **Smooth transitions** between sections
- **Mobile-responsive design** with touch-friendly interactions
- **Keyboard navigation** support

## 🛠️ Technologies Used

- **HTML5** - Semantic markup and structure
- **CSS3** - Advanced styling with CSS Grid, Flexbox, and animations
- **JavaScript (ES6+)** - Interactive functionality and DOM manipulation
- **CSS Variables** - Consistent theming and easy customization
- **Google Fonts** - Orbitron and Rajdhani font families
- **Remix Icons** - Comprehensive icon library

## 🎨 Design System

### Color Palette

- **Primary Background**: `#0a0a0f` (Deep dark blue)
- **Secondary Background**: `#1a1a2e` (Dark navy)
- **Accent Background**: `#16213e` (Muted blue)
- **Neon Cyan**: `#00ffff` (Primary accent)
- **Neon Pink**: `#ff00ff` (Secondary accent)
- **Neon Purple**: `#8a2be2` (Gradient accent)
- **Neon Blue**: `#0080ff` (Gradient accent)

### Typography

- **Headers**: Orbitron (Futuristic, tech-inspired)
- **Body Text**: Rajdhani (Clean, readable)
- **Custom Font**: Hangyaku (Anime-style accent font)

### Animations

- **Fade-in effects** for content sections
- **Hover transformations** for interactive elements
- **Parallax scrolling** for background elements
- **Loading animations** with staggered timing
- **Smooth transitions** using cubic-bezier easing

## 📱 Responsive Design

The website is fully responsive and optimized for:

- **Desktop** (1200px+)
- **Tablet** (768px - 1199px)
- **Mobile** (320px - 767px)

### Mobile Features

- **Hamburger menu** for navigation
- **Touch-friendly buttons** and interactions
- **Optimized image sizes** for faster loading
- **Vertical layouts** for better mobile viewing

## 🚀 Getting Started

1. **Clone or download** the project files
2. **Open `index.html`** in a modern web browser
3. **Explore the features**:
   - Navigate through different sections
   - Try the anime filtering and search
   - View anime detail pages
   - Browse the gallery with different tabs
   - Test the responsive design on different screen sizes

## 📁 File Structure

```
AnimeVerse/
├── index.html              # Main homepage
├── anime-detail.html       # Sample anime detail page
├── style.css              # Main stylesheet
├── anime-detail.css       # Detail page specific styles
├── scripts.js             # Main JavaScript functionality
├── anime-detail.js        # Detail page specific scripts
├── Assets/                # Media files
│   ├── logo.png          # Site logo
│   ├── fabIcon.png       # Favicon
│   ├── All1111111.jpeg   # Sample anime image
│   ├── Hangyaku-G3rpg.ttf # Custom font
│   └── THIS IS 4K ANIME (Jujutsu Kaisen).mp4 # Background video
└── README.md             # Project documentation
```

## 🎯 Key Sections

### Navigation

- **Fixed header** with blur effect on scroll
- **Smooth scrolling** to sections
- **Active state indicators** for current section
- **Mobile hamburger menu** for smaller screens

### Hero Section

- **Video background** with overlay
- **Animated title** with gradient text
- **Call-to-action buttons** with hover effects
- **Statistics counters** with animation

### Featured Anime

- **Large showcase card** with detailed information
- **Play button overlay** for video content
- **Rating system** with star indicators
- **Action buttons** for user interaction

### Anime Grid

- **Filter buttons** for genre selection
- **Search functionality** with real-time results
- **Card hover effects** with information overlay
- **Load more** functionality for pagination

### Gallery

- **Tab navigation** for different content types
- **Image modal** with full-screen viewing
- **Download functionality** for wallpapers
- **Favorite system** for user preferences

## 🔧 Customization

### Adding New Anime

1. **Add anime data** to the HTML structure
2. **Include cover images** in the Assets folder
3. **Update filter categories** if needed
4. **Create detail pages** using the template

### Modifying Colors

1. **Update CSS variables** in the `:root` selector
2. **Adjust gradient combinations** for new color schemes
3. **Test contrast ratios** for accessibility

### Adding Features

1. **Extend JavaScript functions** for new interactions
2. **Add CSS animations** for enhanced user experience
3. **Include additional sections** following the existing pattern

## 🌐 Browser Support

- **Chrome** 80+
- **Firefox** 75+
- **Safari** 13+
- **Edge** 80+

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## 📞 Support

For questions or support, please open an issue in the project repository.

---

**AnimeVerse** - Where anime comes to life through digital storytelling ✨
