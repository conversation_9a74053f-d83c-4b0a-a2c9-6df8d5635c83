// AnimeVerse - Interactive Features
document.addEventListener("DOMContentLoaded", function () {
  // Initialize all features
  initializeLoading();
  initializeNavigation();
  initializeScrollEffects();
  initializeMusicToggle();
  initializeAnimations();
  initializeCounters();
  initializeAnimeFilters();
  initializeGallery();
});

// Loading Screen
function initializeLoading() {
  const loadingScreen = document.getElementById("loadingScreen");

  // Simulate loading time
  setTimeout(() => {
    loadingScreen.classList.add("hidden");

    // Remove from DOM after transition
    setTimeout(() => {
      loadingScreen.remove();
    }, 500);
  }, 3000);
}

// Navigation
function initializeNavigation() {
  const navbar = document.getElementById("navbar");
  const navToggle = document.getElementById("navToggle");
  const navMenu = document.getElementById("navMenu");
  const navLinks = document.querySelectorAll(".nav-link");

  // Navbar scroll effect
  window.addEventListener("scroll", () => {
    if (window.scrollY > 100) {
      navbar.classList.add("scrolled");
    } else {
      navbar.classList.remove("scrolled");
    }
  });

  // Mobile menu toggle
  navToggle.addEventListener("click", () => {
    navToggle.classList.toggle("active");
    navMenu.classList.toggle("active");
  });

  // Close mobile menu when clicking on links
  navLinks.forEach((link) => {
    link.addEventListener("click", () => {
      navToggle.classList.remove("active");
      navMenu.classList.remove("active");
    });
  });

  // Active link highlighting
  navLinks.forEach((link) => {
    link.addEventListener("click", (e) => {
      navLinks.forEach((l) => l.classList.remove("active"));
      link.classList.add("active");
    });
  });
}

// Scroll Effects
function initializeScrollEffects() {
  const scrollProgress = document.getElementById("scrollProgress");

  // Update scroll progress
  window.addEventListener("scroll", () => {
    const scrollTop = window.pageYOffset;
    const docHeight = document.body.scrollHeight - window.innerHeight;
    const scrollPercent = (scrollTop / docHeight) * 100;

    scrollProgress.style.width = scrollPercent + "%";
  });

  // Smooth scroll for navigation links
  document.querySelectorAll('a[href^="#"]').forEach((anchor) => {
    anchor.addEventListener("click", function (e) {
      e.preventDefault();
      const target = document.querySelector(this.getAttribute("href"));
      if (target) {
        target.scrollIntoView({
          behavior: "smooth",
          block: "start",
        });
      }
    });
  });
}

// Music Toggle
function initializeMusicToggle() {
  const musicToggle = document.getElementById("musicToggle");
  const musicIcon = document.getElementById("musicIcon");
  const backgroundMusic = document.getElementById("backgroundMusic");
  let isPlaying = false;

  musicToggle.addEventListener("click", () => {
    if (isPlaying) {
      backgroundMusic.pause();
      musicIcon.className = "ri-volume-mute-line";
      isPlaying = false;
    } else {
      // Note: Auto-play might be blocked by browser policies
      backgroundMusic.play().catch((e) => {
        console.log("Audio play failed:", e);
      });
      musicIcon.className = "ri-volume-up-line";
      isPlaying = true;
    }
  });
}

// Animations
function initializeAnimations() {
  // Intersection Observer for scroll animations
  const observerOptions = {
    threshold: 0.1,
    rootMargin: "0px 0px -50px 0px",
  };

  const observer = new IntersectionObserver((entries) => {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        entry.target.style.animationPlayState = "running";
      }
    });
  }, observerOptions);

  // Observe elements with animations
  document.querySelectorAll('[class*="fadeIn"]').forEach((el) => {
    observer.observe(el);
  });
}

// Counter Animation
function initializeCounters() {
  const counters = document.querySelectorAll(".stat-number");

  const animateCounter = (counter) => {
    const target = parseInt(counter.getAttribute("data-count"));
    const duration = 2000; // 2 seconds
    const step = target / (duration / 16); // 60fps
    let current = 0;

    const timer = setInterval(() => {
      current += step;
      if (current >= target) {
        current = target;
        clearInterval(timer);
      }
      counter.textContent = Math.floor(current);
    }, 16);
  };

  // Intersection Observer for counters
  const counterObserver = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          animateCounter(entry.target);
          counterObserver.unobserve(entry.target);
        }
      });
    },
    { threshold: 0.5 }
  );

  counters.forEach((counter) => {
    counterObserver.observe(counter);
  });
}

// Utility Functions
function scrollToSection(sectionId) {
  const section = document.getElementById(sectionId);
  if (section) {
    section.scrollIntoView({
      behavior: "smooth",
      block: "start",
    });
  }
}

function openAnimeDetail(animeId) {
  // This would typically navigate to a detail page
  // For now, we'll show an alert
  alert(`Opening details for: ${animeId}`);
  // In a real application, you might do:
  // window.location.href = `anime-detail.html?id=${animeId}`;
}

// Parallax Effect for Video Background
window.addEventListener("scroll", () => {
  const scrolled = window.pageYOffset;
  const video = document.getElementById("heroVideo");
  if (video) {
    video.style.transform = `translateY(${scrolled * 0.5}px)`;
  }
});

// Add some interactive hover effects
document.addEventListener("mousemove", (e) => {
  const cursor = document.querySelector(".cursor");
  if (cursor) {
    cursor.style.left = e.clientX + "px";
    cursor.style.top = e.clientY + "px";
  }
});

// Anime Filters and Search
function initializeAnimeFilters() {
  const filterBtns = document.querySelectorAll(".filter-btn");
  const animeCards = document.querySelectorAll(".anime-card");
  const searchInput = document.getElementById("animeSearch");

  // Filter functionality
  filterBtns.forEach((btn) => {
    btn.addEventListener("click", () => {
      // Update active filter button
      filterBtns.forEach((b) => b.classList.remove("active"));
      btn.classList.add("active");

      const filter = btn.getAttribute("data-filter");

      animeCards.forEach((card) => {
        const category = card.getAttribute("data-category");

        if (filter === "all" || category.includes(filter)) {
          card.style.display = "block";
          card.style.animation = "fadeInUp 0.5s ease-out";
        } else {
          card.style.display = "none";
        }
      });
    });
  });

  // Search functionality
  if (searchInput) {
    searchInput.addEventListener("input", (e) => {
      const searchTerm = e.target.value.toLowerCase();

      animeCards.forEach((card) => {
        const title = card
          .querySelector(".anime-card-title")
          .textContent.toLowerCase();
        const synopsis = card
          .querySelector(".anime-card-synopsis")
          .textContent.toLowerCase();

        if (title.includes(searchTerm) || synopsis.includes(searchTerm)) {
          card.style.display = "block";
          card.style.animation = "fadeInUp 0.5s ease-out";
        } else {
          card.style.display = "none";
        }
      });
    });
  }
}

// Gallery functionality
function initializeGallery() {
  const galleryTabs = document.querySelectorAll(".gallery-tab");
  const galleryContents = document.querySelectorAll(".gallery-content");

  galleryTabs.forEach((tab) => {
    tab.addEventListener("click", () => {
      const targetTab = tab.getAttribute("data-tab");

      // Update active tab
      galleryTabs.forEach((t) => t.classList.remove("active"));
      tab.classList.add("active");

      // Update active content
      galleryContents.forEach((content) => {
        content.classList.remove("active");
        if (content.getAttribute("data-content") === targetTab) {
          content.classList.add("active");
        }
      });
    });
  });
}

// Image modal functionality
function openImageModal(button) {
  const galleryItem = button.closest(".gallery-item");
  const img = galleryItem.querySelector("img");
  const title = galleryItem.querySelector(".gallery-info h4").textContent;
  const description = galleryItem.querySelector(".gallery-info p").textContent;

  // Create modal
  const modal = document.createElement("div");
  modal.className = "image-modal-overlay";
  modal.innerHTML = `
    <div class="image-modal">
      <div class="modal-header">
        <h3>${title}</h3>
        <button class="modal-close" onclick="closeImageModal()">
          <i class="ri-close-line"></i>
        </button>
      </div>
      <div class="modal-body">
        <img src="${img.src}" alt="${img.alt}" />
        <p>${description}</p>
      </div>
      <div class="modal-footer">
        <button class="btn btn-primary" onclick="downloadImage('${img.src}', '${title}')">
          <i class="ri-download-line"></i>
          <span>Download</span>
        </button>
        <button class="btn btn-outline">
          <i class="ri-heart-line"></i>
          <span>Add to Favorites</span>
        </button>
      </div>
    </div>
  `;

  document.body.appendChild(modal);
  document.body.style.overflow = "hidden";

  setTimeout(() => {
    modal.classList.add("active");
  }, 10);
}

function closeImageModal() {
  const modal = document.querySelector(".image-modal-overlay");
  if (modal) {
    modal.classList.remove("active");
    setTimeout(() => {
      modal.remove();
      document.body.style.overflow = "";
    }, 300);
  }
}

function downloadImage(src, filename) {
  // Create a temporary link element
  const link = document.createElement("a");
  link.href = src;
  link.download = filename || "anime-image";
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}

// Keyboard navigation
document.addEventListener("keydown", (e) => {
  if (e.key === "Escape") {
    // Close any open modals or menus
    const navMenu = document.getElementById("navMenu");
    const navToggle = document.getElementById("navToggle");

    if (navMenu.classList.contains("active")) {
      navMenu.classList.remove("active");
      navToggle.classList.remove("active");
    }
  }
});
