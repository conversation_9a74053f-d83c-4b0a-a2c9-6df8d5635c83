/* Custom Fonts */
@font-face {
  font-family: Hangyaku;
  src: url(./Assets/Hangyaku-G3rpg.ttf);
}

/* CSS Variables for Theme */
:root {
  --primary-bg: #0a0a0f;
  --secondary-bg: #1a1a2e;
  --accent-bg: #16213e;
  --neon-cyan: #00ffff;
  --neon-pink: #ff00ff;
  --neon-purple: #8a2be2;
  --neon-blue: #0080ff;
  --text-primary: #ffffff;
  --text-secondary: #b8b8b8;
  --text-muted: #666666;
  --gradient-primary: linear-gradient(
    135deg,
    var(--neon-cyan),
    var(--neon-purple)
  );
  --gradient-secondary: linear-gradient(
    135deg,
    var(--neon-pink),
    var(--neon-blue)
  );
  --shadow-neon: 0 0 20px rgba(0, 255, 255, 0.3);
  --shadow-pink: 0 0 20px rgba(255, 0, 255, 0.3);
  --border-radius: 12px;
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  overflow-x: hidden;
}

body {
  font-family: "Rajdhani", "Hangyaku", sans-serif;
  background: var(--primary-bg);
  color: var(--text-primary);
  line-height: 1.6;
  overflow-x: hidden;
}

/* Loading Screen */
.loading-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--primary-bg);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  transition: opacity 0.5s ease, visibility 0.5s ease;
}

.loading-screen.hidden {
  opacity: 0;
  visibility: hidden;
}

.loading-content {
  text-align: center;
}

.loading-logo img {
  width: 120px;
  height: auto;
  margin-bottom: 2rem;
  filter: drop-shadow(var(--shadow-neon));
}

.loading-text {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
  margin-bottom: 2rem;
}

.loading-letter {
  font-family: "Orbitron", monospace;
  font-size: 2rem;
  font-weight: 700;
  color: var(--neon-cyan);
  animation: letterGlow 2s ease-in-out infinite;
  animation-delay: calc(var(--i) * 0.1s);
}

.loading-letter:nth-child(1) {
  --i: 0;
}
.loading-letter:nth-child(2) {
  --i: 1;
}
.loading-letter:nth-child(3) {
  --i: 2;
}
.loading-letter:nth-child(4) {
  --i: 3;
}
.loading-letter:nth-child(5) {
  --i: 4;
}
.loading-letter:nth-child(6) {
  --i: 5;
}
.loading-letter:nth-child(7) {
  --i: 6;
}
.loading-letter:nth-child(8) {
  --i: 7;
}
.loading-letter:nth-child(9) {
  --i: 8;
}
.loading-letter:nth-child(10) {
  --i: 9;
}

@keyframes letterGlow {
  0%,
  100% {
    text-shadow: 0 0 10px var(--neon-cyan);
    transform: translateY(0);
  }
  50% {
    text-shadow: 0 0 20px var(--neon-cyan), 0 0 30px var(--neon-cyan);
    transform: translateY(-10px);
  }
}

.loading-bar {
  width: 300px;
  height: 4px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  overflow: hidden;
  margin: 0 auto;
}

.loading-progress {
  height: 100%;
  background: var(--gradient-primary);
  border-radius: 2px;
  animation: loadingProgress 3s ease-in-out infinite;
}

@keyframes loadingProgress {
  0% {
    width: 0%;
  }
  50% {
    width: 70%;
  }
  100% {
    width: 100%;
  }
}

/* Music Toggle */
.music-toggle {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 50px;
  height: 50px;
  background: rgba(0, 0, 0, 0.7);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 1000;
  transition: var(--transition);
  backdrop-filter: blur(10px);
  user-select: none;
}

.music-toggle:hover {
  background: rgba(0, 255, 255, 0.1);
  border-color: var(--neon-cyan);
  box-shadow: var(--shadow-neon);
  transform: scale(1.1);
}

.music-toggle.playing {
  background: rgba(0, 255, 255, 0.2);
  border-color: var(--neon-cyan);
  box-shadow: var(--shadow-neon);
  animation: musicPulse 2s ease-in-out infinite;
}

.music-toggle.playing:hover {
  background: rgba(0, 255, 255, 0.3);
}

.music-toggle i {
  font-size: 1.5rem;
  color: rgba(255, 255, 255, 0.6);
  transition: var(--transition);
}

.music-toggle:hover i {
  color: var(--neon-cyan);
}

.music-toggle.playing i {
  color: var(--text-primary);
}

@keyframes musicPulse {
  0%,
  100% {
    box-shadow: 0 0 20px rgba(0, 255, 255, 0.3);
  }
  50% {
    box-shadow: 0 0 30px rgba(0, 255, 255, 0.6);
  }
}

/* Scroll Progress */
.scroll-progress {
  position: fixed;
  top: 0;
  left: 0;
  width: 0%;
  height: 3px;
  background: var(--gradient-primary);
  z-index: 1000;
  transition: width 0.1s ease;
}

/* Video Background */
.videoLayer {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -2;
  overflow: hidden;
}

.video-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(10, 10, 15, 0.8) 0%,
    rgba(26, 26, 46, 0.6) 50%,
    rgba(22, 33, 62, 0.8) 100%
  );
  z-index: 1;
}

.videoLayer video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

/* Main Content */
main {
  position: relative;
  z-index: 1;
}

/* Full-Screen Video Player */
.video-player-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.95);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.video-player-overlay.active {
  opacity: 1;
  visibility: visible;
}

.video-player-container {
  position: relative;
  width: 90%;
  max-width: 1200px;
  max-height: 90vh;
  background: var(--secondary-bg);
  border: 2px solid var(--neon-cyan);
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 255, 255, 0.3);
}

.video-close-btn {
  position: absolute;
  top: 15px;
  right: 15px;
  width: 50px;
  height: 50px;
  background: rgba(0, 0, 0, 0.8);
  border: 2px solid var(--neon-cyan);
  border-radius: 50%;
  color: var(--neon-cyan);
  font-size: 1.5rem;
  cursor: pointer;
  z-index: 10001;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
}

.video-close-btn:hover {
  background: var(--neon-cyan);
  color: var(--primary-bg);
  transform: scale(1.1);
  box-shadow: var(--shadow-neon);
}

.video-player-container video {
  width: 100%;
  height: auto;
  max-height: 70vh;
  display: block;
}

.video-info {
  padding: 1.5rem;
  background: linear-gradient(
    135deg,
    rgba(10, 10, 15, 0.9) 0%,
    rgba(26, 26, 46, 0.8) 100%
  );
  border-top: 1px solid rgba(0, 255, 255, 0.2);
}

.video-info h3 {
  font-family: "Orbitron", monospace;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.video-info p {
  color: var(--text-secondary);
  font-size: 1rem;
  line-height: 1.6;
}

/* Video Error and Play Button Overlays */
.video-error {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10002;
}

.error-content {
  text-align: center;
  padding: 2rem;
  background: var(--secondary-bg);
  border: 2px solid var(--neon-pink);
  border-radius: var(--border-radius);
  max-width: 400px;
}

.error-content i {
  font-size: 3rem;
  color: var(--neon-pink);
  margin-bottom: 1rem;
}

.error-content h4 {
  font-family: "Orbitron", monospace;
  font-size: 1.5rem;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.error-content p {
  color: var(--text-secondary);
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.video-play-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10002;
  cursor: pointer;
}

.video-play-btn {
  width: 100px;
  height: 100px;
  background: var(--gradient-primary);
  border: none;
  border-radius: 50%;
  color: var(--text-primary);
  font-size: 2.5rem;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-neon);
}

.video-play-btn:hover {
  transform: scale(1.1);
  box-shadow: 0 0 30px rgba(0, 255, 255, 0.5);
}

.video-play-btn i {
  margin-left: 5px; /* Adjust play icon position */
}

/* Navigation */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  padding: 1rem 0;
  background: rgba(10, 10, 15, 0.9);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(0, 255, 255, 0.1);
  z-index: 1000;
  transition: var(--transition);
}

.navbar.scrolled {
  background: rgba(10, 10, 15, 0.95);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.nav-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.logo {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.logo img {
  height: 50px;
  width: auto;
  filter: drop-shadow(var(--shadow-neon));
}

.logo-text {
  font-family: "Orbitron", monospace;
  font-size: 1.5rem;
  font-weight: 700;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.nav-menu {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  color: var(--text-secondary);
  text-decoration: none;
  border-radius: var(--border-radius);
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

.nav-link::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: var(--gradient-primary);
  transition: var(--transition);
  z-index: -1;
}

.nav-link:hover::before,
.nav-link.active::before {
  left: 0;
}

.nav-link:hover,
.nav-link.active {
  color: var(--text-primary);
  transform: translateY(-2px);
}

.nav-link i {
  font-size: 1.2rem;
}

.nav-toggle {
  display: none;
  flex-direction: column;
  cursor: pointer;
  gap: 4px;
}

.nav-toggle span {
  width: 25px;
  height: 3px;
  background: var(--neon-cyan);
  border-radius: 2px;
  transition: var(--transition);
}

/* Hero Section */
.hero-section {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  padding: 0 2rem;
}

.hero-content {
  max-width: 1400px;
  width: 100%;
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 4rem;
  align-items: center;
}

.hero-text {
  animation: fadeInUp 1s ease-out;
}

.hero-title {
  margin-bottom: 2rem;
}

.title-line {
  display: block;
  font-size: 1.5rem;
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
  animation: fadeInLeft 1s ease-out 0.2s both;
}

.title-main {
  display: block;
  font-family: "Orbitron", monospace;
  font-size: clamp(3rem, 8vw, 6rem);
  font-weight: 900;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 0.5rem;
  animation: fadeInLeft 1s ease-out 0.4s both;
}

.title-subtitle {
  display: block;
  font-size: 1.2rem;
  color: var(--neon-pink);
  font-weight: 300;
  animation: fadeInLeft 1s ease-out 0.6s both;
}

.hero-description {
  font-size: 1.2rem;
  color: var(--text-secondary);
  margin-bottom: 3rem;
  max-width: 600px;
  line-height: 1.8;
  animation: fadeInUp 1s ease-out 0.8s both;
}

.hero-buttons {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 3rem;
  animation: fadeInUp 1s ease-out 1s both;
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 2rem;
  border: none;
  border-radius: var(--border-radius);
  font-family: inherit;
  font-size: 1rem;
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

.btn-primary {
  background: var(--gradient-primary);
  color: var(--text-primary);
  box-shadow: var(--shadow-neon);
}

.btn-primary:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 255, 255, 0.4);
}

.btn-secondary {
  background: transparent;
  color: var(--neon-pink);
  border: 2px solid var(--neon-pink);
}

.btn-secondary:hover {
  background: var(--neon-pink);
  color: var(--primary-bg);
  transform: translateY(-3px);
  box-shadow: var(--shadow-pink);
}

.btn-outline {
  background: transparent;
  color: var(--text-secondary);
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.btn-outline:hover {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-primary);
  border-color: var(--neon-cyan);
}

/* Hero Stats */
.hero-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
  animation: fadeInRight 1s ease-out 1.2s both;
}

.stat-item {
  text-align: center;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(0, 255, 255, 0.2);
  border-radius: var(--border-radius);
  backdrop-filter: blur(10px);
  transition: var(--transition);
}

.stat-item:hover {
  transform: translateY(-5px);
  border-color: var(--neon-cyan);
  box-shadow: var(--shadow-neon);
}

.stat-number {
  display: block;
  font-family: "Orbitron", monospace;
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--neon-cyan);
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 0.9rem;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* Scroll Indicator */
.scroll-indicator {
  position: absolute;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  color: var(--text-secondary);
  animation: fadeInUp 1s ease-out 1.4s both;
}

.scroll-mouse {
  width: 24px;
  height: 40px;
  border: 2px solid var(--neon-cyan);
  border-radius: 12px;
  position: relative;
}

.scroll-wheel {
  width: 4px;
  height: 8px;
  background: var(--neon-cyan);
  border-radius: 2px;
  position: absolute;
  top: 8px;
  left: 50%;
  transform: translateX(-50%);
  animation: scrollWheel 2s ease-in-out infinite;
}

@keyframes scrollWheel {
  0% {
    top: 8px;
    opacity: 1;
  }
  50% {
    top: 20px;
    opacity: 0.5;
  }
  100% {
    top: 8px;
    opacity: 1;
  }
}

.scroll-indicator span {
  font-size: 0.8rem;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* Container */
.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* Section Styles */
.featured-section {
  padding: 8rem 0;
  background: linear-gradient(
    135deg,
    rgba(10, 10, 15, 0.9) 0%,
    rgba(26, 26, 46, 0.8) 50%,
    rgba(22, 33, 62, 0.9) 100%
  );
  position: relative;
}

.section-header {
  text-align: center;
  margin-bottom: 4rem;
}

.section-title {
  font-family: "Orbitron", monospace;
  font-size: clamp(2.5rem, 6vw, 4rem);
  font-weight: 700;
  margin-bottom: 1rem;
}

.title-accent {
  color: var(--neon-pink);
}

.title-main {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.section-subtitle {
  font-size: 1.2rem;
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
}

/* Featured Anime Card */
.featured-anime {
  display: flex;
  justify-content: center;
}

.featured-card {
  display: grid;
  grid-template-columns: 1fr 1.5fr;
  gap: 3rem;
  max-width: 1200px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(0, 255, 255, 0.2);
  border-radius: var(--border-radius);
  overflow: hidden;
  backdrop-filter: blur(20px);
  transition: var(--transition);
}

.featured-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  border-color: var(--neon-cyan);
}

.featured-image {
  position: relative;
  overflow: hidden;
}

.featured-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: var(--transition);
}

.featured-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: var(--transition);
}

.featured-image:hover .featured-overlay {
  opacity: 1;
}

.featured-image:hover img {
  transform: scale(1.1);
}

.play-button {
  width: 80px;
  height: 80px;
  background: var(--gradient-primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition);
}

.play-button:hover {
  transform: scale(1.1);
  box-shadow: var(--shadow-neon);
}

.play-button i {
  font-size: 2rem;
  color: var(--text-primary);
  margin-left: 4px;
}

/* Featured Content */
.featured-content {
  padding: 3rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.anime-meta {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.anime-genre,
.anime-year {
  padding: 0.5rem 1rem;
  background: rgba(0, 255, 255, 0.1);
  color: var(--neon-cyan);
  border: 1px solid var(--neon-cyan);
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.anime-rating {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--neon-pink);
  font-weight: 600;
}

.anime-rating i {
  color: #ffd700;
}

.anime-title {
  font-family: "Orbitron", monospace;
  font-size: 2.5rem;
  font-weight: 700;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 1.5rem;
}

.anime-synopsis {
  font-size: 1.1rem;
  color: var(--text-secondary);
  line-height: 1.8;
  margin-bottom: 2rem;
}

.anime-details {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  margin-bottom: 2rem;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.detail-label {
  font-size: 0.8rem;
  color: var(--text-muted);
  text-transform: uppercase;
  letter-spacing: 1px;
}

.detail-value {
  font-weight: 600;
  color: var(--text-primary);
}

.anime-actions {
  display: flex;
  gap: 1rem;
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .hero-content {
    grid-template-columns: 1fr;
    gap: 3rem;
    text-align: center;
  }

  .hero-stats {
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
  }

  .featured-card {
    grid-template-columns: 1fr;
    max-width: 800px;
  }

  .anime-details {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .nav-menu {
    position: fixed;
    top: 80px;
    left: -100%;
    width: 100%;
    height: calc(100vh - 80px);
    background: rgba(10, 10, 15, 0.95);
    backdrop-filter: blur(20px);
    flex-direction: column;
    justify-content: flex-start;
    padding: 2rem;
    transition: var(--transition);
  }

  .nav-menu.active {
    left: 0;
  }

  .nav-toggle {
    display: flex;
  }

  .nav-toggle.active span:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
  }

  .nav-toggle.active span:nth-child(2) {
    opacity: 0;
  }

  .nav-toggle.active span:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
  }

  .hero-buttons {
    flex-direction: column;
    align-items: center;
  }

  .hero-stats {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .stat-item {
    padding: 1.5rem;
  }

  .featured-content {
    padding: 2rem;
  }

  .anime-details {
    grid-template-columns: 1fr;
  }

  .anime-actions {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 1rem;
  }

  .hero-section {
    padding: 0 1rem;
  }

  .title-main {
    font-size: 3rem;
  }

  .hero-description {
    font-size: 1rem;
  }

  .btn {
    padding: 0.75rem 1.5rem;
    font-size: 0.9rem;
  }

  .featured-content {
    padding: 1.5rem;
  }

  .anime-title {
    font-size: 2rem;
  }
}

/* Anime Index Section */
.anime-index-section {
  padding: 8rem 0;
  background: var(--primary-bg);
  position: relative;
}

/* Filter Controls */
.filter-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 3rem;
  gap: 2rem;
}

.filter-group {
  display: flex;
  gap: 1rem;
}

.filter-btn {
  padding: 0.75rem 1.5rem;
  background: transparent;
  color: var(--text-secondary);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 25px;
  font-family: inherit;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  text-transform: uppercase;
  letter-spacing: 1px;
}

.filter-btn:hover,
.filter-btn.active {
  background: var(--gradient-primary);
  color: var(--text-primary);
  border-color: var(--neon-cyan);
  box-shadow: var(--shadow-neon);
}

.search-box {
  position: relative;
  display: flex;
  align-items: center;
}

.search-box i {
  position: absolute;
  left: 1rem;
  color: var(--text-muted);
  font-size: 1.2rem;
  z-index: 1;
}

.search-box input {
  padding: 0.75rem 1rem 0.75rem 3rem;
  background: rgba(255, 255, 255, 0.05);
  border: 2px solid rgba(0, 255, 255, 0.2);
  border-radius: 25px;
  color: var(--text-primary);
  font-family: inherit;
  font-size: 1rem;
  width: 300px;
  transition: var(--transition);
}

.search-box input:focus {
  outline: none;
  border-color: var(--neon-cyan);
  box-shadow: var(--shadow-neon);
  background: rgba(255, 255, 255, 0.1);
}

.search-box input::placeholder {
  color: var(--text-muted);
}

/* Anime Grid */
.anime-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-bottom: 4rem;
}

.anime-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(0, 255, 255, 0.2);
  border-radius: var(--border-radius);
  overflow: hidden;
  transition: var(--transition);
  backdrop-filter: blur(10px);
  position: relative;
}

.anime-card:hover {
  transform: translateY(-10px);
  border-color: var(--neon-cyan);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.anime-card-image {
  position: relative;
  height: 250px;
  overflow: hidden;
}

.anime-card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: var(--transition);
}

.anime-card:hover .anime-card-image img {
  transform: scale(1.1);
}

.anime-card-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(0, 0, 0, 0.7) 0%,
    rgba(0, 0, 0, 0.3) 50%,
    rgba(0, 0, 0, 0.7) 100%
  );
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 1.5rem;
  opacity: 0;
  transition: var(--transition);
}

.anime-card:hover .anime-card-overlay {
  opacity: 1;
}

.anime-card-info h3 {
  font-family: "Orbitron", monospace;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.anime-card-info p {
  color: var(--text-secondary);
  margin-bottom: 1rem;
}

.anime-card-info .anime-rating {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--neon-pink);
  font-weight: 600;
}

.anime-card-info .anime-rating i {
  color: #ffd700;
}

.anime-card-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.card-btn {
  width: 45px;
  height: 45px;
  background: rgba(0, 255, 255, 0.2);
  border: 2px solid var(--neon-cyan);
  border-radius: 50%;
  color: var(--neon-cyan);
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
}

.card-btn:hover {
  background: var(--neon-cyan);
  color: var(--primary-bg);
  transform: scale(1.1);
  box-shadow: var(--shadow-neon);
}

.anime-card-content {
  padding: 1.5rem;
}

.anime-card-title {
  font-family: "Orbitron", monospace;
  font-size: 1.3rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 0.75rem;
}

.anime-card-synopsis {
  color: var(--text-secondary);
  font-size: 0.9rem;
  line-height: 1.6;
  margin-bottom: 1rem;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.anime-card-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.anime-episodes,
.anime-year {
  font-size: 0.8rem;
  color: var(--text-muted);
  padding: 0.25rem 0.75rem;
  background: rgba(0, 255, 255, 0.1);
  border: 1px solid rgba(0, 255, 255, 0.3);
  border-radius: 15px;
}

/* Load More */
.load-more-container {
  text-align: center;
}

.load-more-btn {
  padding: 1rem 3rem;
  font-size: 1.1rem;
}

/* Additional Responsive Design for Anime Index */
@media (max-width: 1024px) {
  .filter-controls {
    flex-direction: column;
    gap: 1.5rem;
    align-items: stretch;
  }

  .filter-group {
    justify-content: center;
    flex-wrap: wrap;
  }

  .search-box input {
    width: 100%;
  }

  .anime-grid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
  }
}

@media (max-width: 768px) {
  .filter-group {
    gap: 0.5rem;
  }

  .filter-btn {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
  }

  .anime-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1rem;
  }

  .anime-card-image {
    height: 200px;
  }

  .anime-card-content {
    padding: 1rem;
  }

  .anime-card-title {
    font-size: 1.1rem;
  }
}

@media (max-width: 480px) {
  .anime-index-section {
    padding: 4rem 0;
  }

  .filter-group {
    flex-direction: column;
    gap: 0.75rem;
  }

  .filter-btn {
    width: 100%;
    text-align: center;
  }

  .anime-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .anime-card-image {
    height: 180px;
  }

  .anime-card-overlay {
    padding: 1rem;
  }

  .anime-card-info h3 {
    font-size: 1.2rem;
  }

  .anime-card-actions {
    gap: 0.5rem;
  }

  .card-btn {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }
}

/* Gallery Section */
.gallery-section {
  padding: 8rem 0;
  background: linear-gradient(
    135deg,
    rgba(10, 10, 15, 0.9) 0%,
    rgba(26, 26, 46, 0.8) 50%,
    rgba(22, 33, 62, 0.9) 100%
  );
}

.gallery-tabs {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 3rem;
  flex-wrap: wrap;
}

.gallery-tab {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 2rem;
  background: transparent;
  color: var(--text-secondary);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 25px;
  font-family: inherit;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
}

.gallery-tab:hover,
.gallery-tab.active {
  background: var(--gradient-primary);
  color: var(--text-primary);
  border-color: var(--neon-cyan);
  box-shadow: var(--shadow-neon);
}

.gallery-tab i {
  font-size: 1.2rem;
}

.gallery-content {
  display: none;
}

.gallery-content.active {
  display: block;
}

.gallery-masonry {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  grid-auto-rows: masonry;
}

.gallery-item {
  position: relative;
  border-radius: var(--border-radius);
  overflow: hidden;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(0, 255, 255, 0.2);
  transition: var(--transition);
  cursor: pointer;
}

.gallery-item:hover {
  transform: translateY(-5px);
  border-color: var(--neon-cyan);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
}

.gallery-item img {
  width: 100%;
  height: 250px;
  object-fit: cover;
  transition: var(--transition);
}

.gallery-item:hover img {
  transform: scale(1.05);
}

.gallery-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(0, 0, 0, 0.7) 0%,
    rgba(0, 0, 0, 0.3) 50%,
    rgba(0, 0, 0, 0.7) 100%
  );
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 1.5rem;
  opacity: 0;
  transition: var(--transition);
}

.gallery-item:hover .gallery-overlay {
  opacity: 1;
}

.gallery-info h4 {
  font-family: "Orbitron", monospace;
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.gallery-info p {
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.gallery-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.gallery-btn {
  width: 45px;
  height: 45px;
  background: rgba(0, 255, 255, 0.2);
  border: 2px solid var(--neon-cyan);
  border-radius: 50%;
  color: var(--neon-cyan);
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
}

.gallery-btn:hover {
  background: var(--neon-cyan);
  color: var(--primary-bg);
  transform: scale(1.1);
  box-shadow: var(--shadow-neon);
}

/* About Section */
.about-section {
  padding: 2rem 0;
  background: var(--primary-bg);
}

.about-content {
  max-width: 1000px;
  margin: 0 auto;
  text-align: center;
}

.about-text h3 {
  font-family: "Orbitron", monospace;
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 2rem;
}

.about-text p {
  font-size: 1.1rem;
  color: var(--text-secondary);
  line-height: 1.8;
  margin-bottom: 2rem;
}

.about-features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-top: 4rem;
}

.feature-item {
  text-align: center;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(0, 255, 255, 0.2);
  border-radius: var(--border-radius);
  transition: var(--transition);
}

.feature-item:hover {
  border-color: var(--neon-cyan);
  box-shadow: var(--shadow-neon);
  transform: translateY(-5px);
}

.feature-item i {
  font-size: 3rem;
  color: var(--neon-cyan);
  margin-bottom: 1rem;
}

.feature-item h4 {
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.feature-item p {
  color: var(--text-secondary);
  line-height: 1.6;
}

/* Image Modal */
.image-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.image-modal-overlay.active {
  opacity: 1;
  visibility: visible;
}

.image-modal {
  background: var(--secondary-bg);
  border: 2px solid var(--neon-cyan);
  border-radius: var(--border-radius);
  max-width: 90vw;
  max-height: 90vh;
  overflow: hidden;
  transform: scale(0.8);
  transition: transform 0.3s ease;
}

.image-modal-overlay.active .image-modal {
  transform: scale(1);
}

.image-modal .modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid rgba(0, 255, 255, 0.2);
}

.image-modal .modal-header h3 {
  font-family: "Orbitron", monospace;
  color: var(--neon-cyan);
  margin: 0;
}

.image-modal .modal-close {
  background: none;
  border: none;
  color: var(--text-primary);
  font-size: 1.5rem;
  cursor: pointer;
  transition: var(--transition);
}

.image-modal .modal-close:hover {
  color: var(--neon-cyan);
}

.image-modal .modal-body {
  padding: 2rem;
  text-align: center;
}

.image-modal .modal-body img {
  max-width: 100%;
  max-height: 60vh;
  border-radius: var(--border-radius);
  margin-bottom: 1rem;
}

.image-modal .modal-body p {
  color: var(--text-secondary);
  font-size: 1.1rem;
}

.image-modal .modal-footer {
  display: flex;
  gap: 1rem;
  padding: 1.5rem;
  border-top: 1px solid rgba(0, 255, 255, 0.2);
  justify-content: center;
}

/* Responsive Gallery */
@media (max-width: 1024px) {
  .gallery-masonry {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }

  .gallery-tabs {
    gap: 0.5rem;
  }

  .gallery-tab {
    padding: 0.75rem 1.5rem;
    font-size: 0.9rem;
  }
}

@media (max-width: 768px) {
  .gallery-section {
    padding: 4rem 0;
  }

  .gallery-tabs {
    flex-direction: column;
    align-items: center;
  }

  .gallery-tab {
    width: 200px;
    justify-content: center;
  }

  .gallery-masonry {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
  }

  .gallery-item img {
    height: 200px;
  }

  .about-features {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
  }

  .feature-item {
    padding: 1.5rem;
  }
}

@media (max-width: 480px) {
  .gallery-masonry {
    grid-template-columns: 1fr;
  }

  .gallery-item img {
    height: 180px;
  }

  .gallery-overlay {
    padding: 1rem;
  }

  .gallery-actions {
    gap: 0.5rem;
  }

  .gallery-btn {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }

  .about-features {
    grid-template-columns: 1fr;
  }

  .image-modal {
    max-width: 95vw;
    max-height: 95vh;
  }

  .image-modal .modal-body {
    padding: 1rem;
  }

  .image-modal .modal-footer {
    flex-direction: column;
    padding: 1rem;
  }

  /* Video Player Responsive */
  .video-player-container {
    width: 95%;
    max-height: 95vh;
  }

  .video-close-btn {
    width: 45px;
    height: 45px;
    font-size: 1.3rem;
    top: 10px;
    right: 10px;
  }

  .video-player-container video {
    max-height: 60vh;
  }

  .video-info {
    padding: 1rem;
  }

  .video-info h3 {
    font-size: 1.2rem;
  }

  .video-info p {
    font-size: 0.9rem;
  }
}
